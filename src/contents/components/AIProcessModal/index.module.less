.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 10000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.modalContainer {
  position: absolute;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  width: 480px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border: 1px solid #e1e5e9;
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafbfc;
}

.modalTitle {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #1c1c1e;
}

.aiIcon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  color: #8e8e93;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
    color: #1c1c1e;
  }
}

.processingStatus {
  padding: 12px 20px;
  background: #f8f9fa;
  color: #6c757d;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  
  &::before {
    content: '🤔';
    margin-right: 8px;
  }
}

.modalContent {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
  line-height: 1.6;
  color: #1c1c1e;
  font-size: 15px;
}

.streamContent {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin-bottom: 16px;
  
  .cursor {
    animation: blink 1s infinite;
    color: #007aff;
    font-weight: bold;
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.contentStats {
  padding: 8px 12px;
  background: #f0f8ff;
  border: 1px solid #e1f0ff;
  border-radius: 6px;
  font-size: 13px;
  color: #0066cc;
  margin-top: 12px;
}

.modalActions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafbfc;
  flex-wrap: wrap;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 13px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  span {
    font-size: 14px;
  }
}

.primaryButton {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: auto;

  &:hover {
    background: #0056cc;
  }
}

/* 滚动条样式 */
.modalContent::-webkit-scrollbar {
  width: 6px;
}

.modalContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modalContent::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.modalContent::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .modalContainer {
    width: 95vw;
    margin: 10px;
  }
  
  .modalActions {
    flex-direction: column;
    align-items: stretch;
    
    .actionButton,
    .primaryButton {
      justify-content: center;
      margin-left: 0;
    }
  }
}
