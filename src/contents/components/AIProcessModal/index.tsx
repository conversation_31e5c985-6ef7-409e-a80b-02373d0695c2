import React, { useState, useEffect, useRef } from 'react';
import { browser } from 'webextension-polyfill-ts';
import * as styles from "./index.module.less";

interface AIProcessModalProps {
  isVisible: boolean;
  selectedText: string;
  actionType: string;
  onClose: () => void;
  position?: { x: number; y: number };
}

interface StreamMessage {
  content: string;
  isComplete: boolean;
}

const AIProcessModal: React.FC<AIProcessModalProps> = ({
  isVisible,
  selectedText,
  actionType,
  onClose,
  position = { x: 0, y: 0 }
}) => {
  const [streamMessage, setStreamMessage] = useState<StreamMessage>({
    content: '',
    isComplete: false
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingTime, setProcessingTime] = useState(0);
  const modalRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取操作类型的中文名称
  const getActionName = (action: string): string => {
    const actionMap: Record<string, string> = {
      'summary': '总结',
      'translate': '翻译',
      'abbreviate': '缩写',
      'expand': '扩写',
      'polish': '润色',
      'correct': '修正'
    };
    return actionMap[action] || action;
  };

  // 模拟流式消息处理
  const simulateStreamProcessing = () => {
    setIsProcessing(true);
    setProcessingTime(0);
    setStreamMessage({ content: '', isComplete: false });

    // 启动计时器
    timerRef.current = setInterval(() => {
      setProcessingTime(prev => prev + 1);
    }, 1000);

    // 模拟流式响应
    const mockResponse = generateMockResponse(actionType, selectedText);
    let currentIndex = 0;
    
    const streamInterval = setInterval(() => {
      if (currentIndex < mockResponse.length) {
        const chunkSize = Math.random() * 10 + 5; // 随机块大小
        const chunk = mockResponse.slice(currentIndex, currentIndex + chunkSize);
        currentIndex += chunkSize;
        
        setStreamMessage(prev => ({
          content: prev.content + chunk,
          isComplete: false
        }));
      } else {
        // 流式处理完成
        setStreamMessage(prev => ({ ...prev, isComplete: true }));
        setIsProcessing(false);
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        clearInterval(streamInterval);
      }
    }, 100 + Math.random() * 200); // 随机间隔
  };

  // 生成模拟响应内容
  const generateMockResponse = (action: string, text: string): string => {
    const responses: Record<string, string> = {
      'summary': `已深度思考（用时3秒）^

嗯，用户要求我作为专业改写专家，将一段中文内容改写更正式官方。让我看看原文："${text.slice(0, 50)}${text.length > 50 ? '...' : ''}"。

这句子很有诗意韵味。"${text.slice(0, 20)}"是南京的历史名巷，"王羲之"是书圣，"墨韵风骨"体现了书法的神韵。

${text}`,
      'translate': `Translation of "${text.slice(0, 30)}${text.length > 30 ? '...' : ''}":

${text}

This text appears to be in Chinese and discusses historical and cultural elements. The translation maintains the poetic and cultural nuances of the original text.`,
      'abbreviate': `原文缩写版本：

${text.split('').slice(0, Math.floor(text.length * 0.6)).join('')}

已生成内容 ${Math.floor(text.length * 0.6)} 字`,
      'expand': `扩写内容：

${text}

进一步阐述：这段文字蕴含着深厚的文化底蕴，体现了中华文明的博大精深。从历史的角度来看，这样的表达方式承载着丰富的文化内涵，值得我们深入思考和品味。

已生成内容 ${text.length + 100} 字`,
      'polish': `润色后的内容：

${text}

润色说明：保持了原文的核心意思，同时优化了表达方式，使语言更加流畅自然，增强了文字的感染力和可读性。`,
      'correct': `修正后的内容：

${text}

修正说明：检查了语法、拼写和语义，确保表达准确无误。原文整体质量较高，仅做了细微调整以提升准确性。`
    };
    
    return responses[action] || `处理结果：${text}`;
  };

  // 处理关闭
  const handleClose = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setIsProcessing(false);
    setStreamMessage({ content: '', isComplete: false });
    setProcessingTime(0);
    onClose();
  };

  // 复制到剪贴板
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(streamMessage.content);
      // 可以添加复制成功的提示
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  // 替换原文
  const handleReplace = () => {
    // TODO: 实现替换原文功能
    console.log('替换原文:', streamMessage.content);
  };

  // 当组件显示时开始处理
  useEffect(() => {
    if (isVisible && selectedText) {
      simulateStreamProcessing();
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isVisible, selectedText, actionType]);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        handleClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className={styles.modalOverlay}>
      <div 
        ref={modalRef}
        className={styles.modalContainer}
        style={{
          left: position.x,
          top: position.y,
        }}
      >
        {/* 头部 */}
        <div className={styles.modalHeader}>
          <div className={styles.modalTitle}>
            <div className={styles.aiIcon}>
              <img
                src={browser.runtime.getURL('assets/icon.png')}
                alt="AI"
              />
            </div>
            <span>{getActionName(actionType)}</span>
          </div>
          <button className={styles.closeButton} onClick={handleClose}>
            ×
          </button>
        </div>

        {/* 处理状态 */}
        {isProcessing && (
          <div className={styles.processingStatus}>
            已深度思考（用时{processingTime}秒）^
          </div>
        )}

        {/* 内容区域 */}
        <div className={styles.modalContent}>
          {streamMessage.content && (
            <div className={styles.streamContent}>
              {streamMessage.content}
              {isProcessing && <span className={styles.cursor}>|</span>}
            </div>
          )}
          
          {streamMessage.isComplete && (
            <div className={styles.contentStats}>
              已生成内容 {streamMessage.content.length} 字
            </div>
          )}
        </div>

        {/* 底部操作按钮 */}
        {streamMessage.isComplete && (
          <div className={styles.modalActions}>
            <button className={styles.actionButton} onClick={() => console.log('继续问')}>
              <span>🔄</span>
              继续问
            </button>
            <button className={styles.actionButton} onClick={() => console.log('调整')}>
              <span>⚙️</span>
              调整
            </button>
            <button className={styles.actionButton} onClick={handleCopy}>
              <span>📋</span>
              弃用
            </button>
            <button className={styles.actionButton} onClick={() => console.log('插入到下方')}>
              <span>📝</span>
              插入到下方
            </button>
            <button className={styles.primaryButton} onClick={handleReplace}>
              替换原文
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIProcessModal;
