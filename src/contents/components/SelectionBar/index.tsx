import React, { useState } from 'react';
import { browser } from 'webextension-polyfill-ts';
import AIProcessModal from '../AIProcessModal';
import * as styles from "./index.module.less";

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [currentAction, setCurrentAction] = useState('');
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });

  const handleAction = (action: string) => {
    // AI处理类操作显示弹窗
    const aiActions = ['summary', 'translate', 'abbreviate', 'expand', 'polish', 'correct'];

    if (aiActions.includes(action)) {
      setCurrentAction(action);
      setShowModal(true);
      setShowDropdown(false);

      // 计算弹窗位置（在SelectionBar下方）
      const barElement = document.querySelector('[class*="selectionBar"]') as HTMLElement;
      if (barElement) {
        const rect = barElement.getBoundingClientRect();
        setModalPosition({
          x: rect.left,
          y: rect.bottom + 10
        });
      }
    } else {
      // 其他操作保持原有逻辑
      onAction(action);
      if (action !== 'open-panel') {
        onClose();
      }
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setShowDropdown(!showDropdown);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setCurrentAction('');
    onClose(); // 同时关闭SelectionBar
  };

  return (
    <>
      <div className={styles.selectionContainer}>
        {/* 箭头指向选中文本 */}
        <div className={styles.selectionArrow} />

        <div className={styles.selectionBar}>
      <div
        className={styles.selectionIcon}
        onClick={() => handleAction('open-panel')}
        title="AI助手"
      >
        {/* <img
          src={browser.runtime.getURL('assets/icon.png')}
          alt="AI"
        /> */}
      </div>

      <div
        className={styles.selectionButton}
        onClick={() => handleAction('summary')}
      >
        {/* <img
          src={browser.runtime.getURL('assets/summary-icon.svg')}
          alt="总结"
        /> */}
        <span>总结</span>
      </div>

      <div
        className={styles.selectionButton}
        onClick={() => handleAction('translate')}
      >
        {/* <img
          src={browser.runtime.getURL('assets/translate-icon.svg')}
          alt="翻译"
        /> */}
        <span>翻译</span>
      </div>

      <div className={styles.selectionMore}>
        <div
          className={styles.selectionDots}
          onClick={toggleDropdown}
          onMouseDown={(e) => e.stopPropagation()}
        >
          ⋮
        </div>
        {showDropdown && (
          <div
            className={`${styles.selectionDropdown} ${styles.show}`}
            onClick={(e) => e.stopPropagation()}
          >
            <div
              className={styles.selectionDropdownItem}
              onClick={(e) => {
                e.stopPropagation();
                handleAction('abbreviate');
              }}
            >
              缩写
            </div>
            <div
              className={styles.selectionDropdownItem}
              onClick={(e) => {
                e.stopPropagation();
                handleAction('expand');
              }}
            >
              扩写
            </div>
            <div
              className={styles.selectionDropdownItem}
              onClick={(e) => {
                e.stopPropagation();
                handleAction('polish');
              }}
            >
              润色
            </div>
            <div
              className={styles.selectionDropdownItem}
              onClick={(e) => {
                e.stopPropagation();
                handleAction('correct');
              }}
            >
              修正
            </div>
          </div>
        )}
      </div>

      {/* 分隔线 */}
      <div className={styles.selectionDivider} />

      <div
        className={styles.selectionClose}
        onClick={onClose}
        title="关闭"
      >
        ×
        </div>
        </div>
      </div>

      {/* AI处理弹窗 */}
      <AIProcessModal
        isVisible={showModal}
        selectedText={selectedText}
        actionType={currentAction}
        onClose={handleModalClose}
        position={modalPosition}
      />
    </>
  );
};

export default SelectionBar;
