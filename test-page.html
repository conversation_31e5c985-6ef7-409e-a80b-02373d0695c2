<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>划词Bar功能测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            border-bottom: 2px solid #007aff;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #555;
            margin-top: 30px;
        }
        
        p {
            margin-bottom: 15px;
            color: #333;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        
        .test-section {
            border: 2px dashed #007aff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background-color: #f8f9ff;
        }
        
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>🚀 AI Chrome扩展 - 划词Bar功能测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p><strong>如何测试：</strong></p>
            <ol>
                <li>确保Chrome扩展已安装并启用</li>
                <li>选中下面任意一段文字</li>
                <li>观察是否出现划词工具栏</li>
                <li>点击工具栏中的各种功能按钮</li>
                <li>查看是否弹出AI处理弹窗</li>
                <li>观察流式消息的渲染效果</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>📝 测试文本区域 1 - 中文诗词</h2>
            <p>乌衣巷口的夕阳余晖，至今仍映射着书圣王羲之的墨韵风骨。这句话承载着深厚的历史文化底蕴，将古代文人的风采与现代的文化传承巧妙地融合在一起。</p>
            
            <p>王羲之作为"书圣"，其书法艺术不仅在当时独领风骚，更是影响了后世千年的文化发展。乌衣巷作为六朝古都南京的历史见证，见证了无数文人墨客的风流雅韵。</p>
        </div>

        <div class="test-section">
            <h2>🌍 测试文本区域 2 - 英文内容</h2>
            <p>Artificial Intelligence has revolutionized the way we interact with technology. From natural language processing to computer vision, AI systems are becoming increasingly sophisticated and capable of handling complex tasks that were once thought to be exclusively human domains.</p>
            
            <p>The development of large language models has particularly transformed how we approach text generation, translation, and content creation. These models can understand context, generate coherent responses, and even exhibit creative capabilities.</p>
        </div>

        <div class="test-section">
            <h2>📚 测试文本区域 3 - 技术文档</h2>
            <p>React是一个用于构建用户界面的JavaScript库，由Facebook开发并维护。它采用组件化的开发模式，使得代码更加模块化和可重用。React的核心概念包括虚拟DOM、组件生命周期、状态管理等。</p>
            
            <p>在现代前端开发中，React配合TypeScript可以提供更好的类型安全和开发体验。通过使用Hook、Context等特性，开发者可以更优雅地管理组件状态和副作用。</p>
        </div>

        <div class="highlight">
            <h3>🎯 功能测试重点</h3>
            <p><strong>请重点测试以下功能：</strong></p>
            <ul>
                <li><strong>总结</strong> - 直接按钮，选中长段落测试内容总结功能</li>
                <li><strong>翻译</strong> - 直接按钮，选中中英文内容测试翻译功能</li>
                <li><strong>三点菜单 (⋮)</strong> - 点击后应显示下拉菜单，包含：</li>
                <ul>
                    <li><strong>缩写</strong> - 选中冗长的文本，测试缩写功能</li>
                    <li><strong>扩写</strong> - 选中简短的文本，测试扩写功能</li>
                    <li><strong>润色</strong> - 选中需要优化的文字，测试润色功能</li>
                    <li><strong>修正</strong> - 选中有语法问题的文本，测试修正功能</li>
                </ul>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔧 测试文本区域 4 - 包含错误的文本</h2>
            <p>这是一个包含一些语法错误和拼写错误的段落，用来测试修正功能。比如：这个句子的语法有问题的，还有一些拼写错误如"错误"写成了"错误"。</p>
            
            <p>人工智能技术在近年来发展迅速，特别是在自然语言处理领域取得了显著的进展。这些技术不仅能够理解人类语言，还能生成高质量的文本内容。</p>
        </div>

        <div class="test-section">
            <h2>📖 测试文本区域 5 - 需要扩写的简短内容</h2>
            <p>AI很强大。</p>
            <p>技术改变世界。</p>
            <p>未来可期。</p>
        </div>

        <div class="instructions">
            <h3>✅ 预期行为</h3>
            <p><strong>正常情况下应该看到：</strong></p>
            <ol>
                <li>选中文字后出现划词工具栏，包含：AI图标、总结、翻译按钮和三点菜单(⋮)</li>
                <li>点击三点菜单应显示下拉菜单：缩写、扩写、润色、修正</li>
                <li>点击任意功能按钮后，工具栏消失，弹出AI处理弹窗</li>
                <li>弹窗显示处理状态和计时器（如：已深度思考（用时3秒）^）</li>
                <li>流式消息逐字显示，带有闪烁光标效果</li>
                <li>处理完成后显示操作按钮（继续问、调整、弃用、插入到下方、替换原文）</li>
                <li>点击关闭按钮或弹窗外部可以关闭弹窗</li>
            </ol>
        </div>
    </div>
</body>
</html>
